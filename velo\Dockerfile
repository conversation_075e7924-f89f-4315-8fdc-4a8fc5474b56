FROM ghcr.io/veloera/veloera:latest

# 安装必要的工具和更新CA证书
RUN apk update && apk add --no-cache ca-certificates tzdata && update-ca-certificates

# 设置环境变量
ENV SQL_DSN="avnadmin:AVNS_toPKq0386tJ2m6HnwCU@tcp(veloera-project-0895.c.aivencloud.com:23461)/defaultdb?parseTime=true&tls=skip-verify&allowNativePasswords=true&charset=utf8mb4"
ENV REDIS_CONN_STRING=""
ENV TZ="Asia/Shanghai"
ENV PORT=3000

# 创建用户和数据目录
RUN adduser -D -u 1000 app || true
RUN mkdir -p /data && chown -R app:app /data

# 切换到应用用户
USER app

# 设置工作目录
WORKDIR /data

# 暴露端口
EXPOSE 3000