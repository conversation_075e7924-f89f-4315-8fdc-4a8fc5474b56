#article-container a:not(.headerlink, .fancybox, .default-style a) {
    font-weight: 700;
    color: var(--font-color);
    padding: 0 3px;
    border-bottom: 2px var(--leonus-purple) solid;
}

#article-container a:not(.headerlink, .fancybox, .default-style a):hover {
    color: #fff;
    border-radius: 5px;
    text-decoration: none;
    background-color: var(--leonus-purple);
}

#danmu {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 60px;
    z-index: 1;
    pointer-events: none;
}

.hidedanmu {
    opacity: 0;
}

.hidedanmu * {
    pointer-events: none !important;
}

div#danmuBtn {
    display: flex;
    justify-content: center;
}

div#danmuBtn button {
    background: var(--leonus-purple);
    color: white;
    padding: 8px 20px;
    margin: 0 20px;
    border-radius: 100px;
}

.default-style {
    pointer-events: all;
    cursor: pointer;
    font-size: 16px;
    border-radius: 100px;
    overflow: hidden;
}

.default-style a {
    background-color: rgba(0, 0, 0, 0.5);
    transition: .3s;
    color: #eee !important;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 16px 6px 6px;
    text-decoration: none !important;
}

.default-style a:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

.default-style img {
    pointer-events: none;
    height: 30px;
    width: 30px;
    margin: 0 5px 0 0 !important;
    border-radius: 50% !important;
}

.default-style p {
    line-height: 1;
    pointer-events: none;
    margin: 0 !important;
    max-width: 500px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
