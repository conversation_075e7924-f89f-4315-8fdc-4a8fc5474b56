---
title: Veloera
emoji: ⚡
colorFrom: indigo
colorTo: gray
sdk: docker
pinned: false
app_port: 3000
---

# Veloera AI API Gateway

优秀的 AI API 网关系统，支持多种 AI 模型提供商。

## 重要配置说明

### 环境变量配置

**重要：** 数据库连接信息需要在 Hugging Face Space 的环境变量中配置，不要写在代码中！

在 Hugging Face Space 设置中添加以下环境变量：

```
SQL_DSN=avnadmin:AVNS_toPKq0386tJ2m6HnwCU@tcp(veloera-project-0895.c.aivencloud.com:23467)/defaultdb?parseTime=true&tls=skip-verify&allowNativePasswords=true&charset=utf8mb4
REDIS_CONN_STRING=
PORT=3000
TZ=Asia/Shanghai
```

### MySQL 连接问题解决

- **端口号**: 确保使用正确的端口 23467（不是 23461）
- **连接参数**: 使用 `tls=skip-verify` 跳过SSL证书验证
- **协议兼容**: 使用 `allowNativePasswords=true` 解决协议问题

### 首次使用

1. 访问应用后使用默认账户登录：
   - 用户名：`root`
   - 密码：`123456`

2. 登录后请立即修改密码

## 参考文档

- [Hugging Face Spaces 配置参考](https://huggingface.co/docs/hub/spaces-config-reference)
- [Veloera 官方文档](https://github.com/Veloera/Veloera)
